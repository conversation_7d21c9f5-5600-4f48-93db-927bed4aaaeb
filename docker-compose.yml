services:
  jassistant:
    image: jassistant:latest
    container_name: jassistant
    network_mode: bridge
    restart: always
    ports:
      - "34711:34711"  # 映射到主机端口，可根据需要修改
    # 日志轮替设置：限制每个日志文件最大为10MB，最多保留3个日志文件
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    volumes:
      # 应用数据目录映射
      - ./data/logs:/app/logs
      - ./data/db:/app/db
      - ./data/settings:/app/settings
      - ./data/watermarks:/app/assets
      - ./data/cache:/app/data/cache
      - ./data/cover_cache:/app/cover_cache
      # 媒体文件目录映射 - 请根据实际情况修改路径
      - /path/to/your/media:/media
    environment:
      - TZ=Asia/Shanghai
      # 设置Python环境变量
      - PYTHONUNBUFFERED=1
      # 设置API密钥和URL，如果需要可以修改
      - CID_API_KEY=your_api_key_here
      - CID_API_URL=http://your-api-server:port/api/get_cid
