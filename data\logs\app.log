2025-07-29 22:16:22,183 INFO: 系统日志已被管理员清除 [in /app/api/log_api.py:112]
2025-07-29 22:16:33,158 INFO: 预加载远程图片: https://awsimgsrc.dmm.co.jp/pics_dig/digital/video/mida00126/mida00126pl.jpg [in /app/api/image_api.py:338]
2025-07-29 22:16:33,262 INFO: 图片预加载成功: https://awsimgsrc.dmm.co.jp/pics_dig/digital/video/mida00126/mida00126pl.jpg -> mida00126pl_1e9a6796.jpg [in /app/api/image_api.py:349]
2025-07-29 22:16:39,413 INFO: 预加载远程图片: https://awsimgsrc.dmm.co.jp/pics_dig/digital/video/mida00126/mida00126ps.jpg [in /app/api/image_api.py:338]
2025-07-29 22:16:39,463 INFO: 图片预加载成功: https://awsimgsrc.dmm.co.jp/pics_dig/digital/video/mida00126/mida00126ps.jpg -> mida00126ps_5531add4.jpg [in /app/api/image_api.py:349]
2025-07-30 00:18:10,108 INFO: === Jassistant v1.0.6 Web服务启动成功 === [in /app/app.py:122]
2025-07-30 00:18:10,108 INFO: Web服务已在端口34711启动，PID: 7 [in /app/app.py:123]
2025-07-30 00:18:10,110 INFO: 数据库初始化完成 [in /app/app.py:129]
2025-07-30 00:18:10,111 INFO: 性能监控系统已启动 [in /app/app.py:136]
2025-07-30 00:18:10,111 INFO: Web服务初始化完成，调度器由独立进程管理 [in /app/app.py:142]
2025-07-30 00:18:32,188 INFO: 开始批量导入，扫描路径: /weiam/HMN-720 [in /app/api/batch_import_api.py:346]
2025-07-30 00:18:32,188 INFO: 开始扫描目录: /weiam/HMN-720 [in /app/api/batch_import_api.py:353]
2025-07-30 00:18:32,188 INFO: 开始调用 find_strm_files... [in /app/api/batch_import_api.py:362]
2025-07-30 00:18:32,188 INFO: find_strm_files: 开始扫描 /weiam/HMN-720 [in /app/api/batch_import_api.py:107]
2025-07-30 00:18:32,188 INFO: 扫描目录 /weiam/HMN-720，找到 10 个项目 [in /app/api/batch_import_api.py:116]
2025-07-30 00:18:32,189 INFO: 找到strm文件: /weiam/HMN-720/HMN-720-破解-C.strm [in /app/api/batch_import_api.py:123]
2025-07-30 00:18:32,189 INFO: 找到strm文件: /weiam/HMN-720/HMN-720-C.strm [in /app/api/batch_import_api.py:123]
2025-07-30 00:18:32,189 INFO: find_strm_files: 扫描完成，共找到 2 个strm文件 [in /app/api/batch_import_api.py:130]
2025-07-30 00:18:32,189 INFO: find_strm_files 返回了 2 个文件 [in /app/api/batch_import_api.py:364]
2025-07-30 00:18:32,190 INFO: 找到 2 个strm文件 [in /app/api/batch_import_api.py:367]
2025-07-30 00:18:32,466 INFO: 批量导入完成，总计: 2 成功 (新增: 2, 覆盖: 0, 跳过: 0), 失败: 0 [in /app/api/batch_import_api.py:414]
